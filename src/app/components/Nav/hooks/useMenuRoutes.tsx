import React, { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import * as AllIcons from '@ghq-abi/design-system-icons';
import { useTranslate } from '@tolgee/react';
import { uniqBy } from 'lodash';

import { MenuItem, RouteObject } from '~/app/types';
import { getMenuItems } from '~/shared/services/menu';

export function useMenuRoutes() {
  const { t } = useTranslate();
  const session = useSession();
  const [menuRoutes, setMenuRoutes] = useState<RouteObject[]>([]);
  const [footerRoutes, setFooterRoutes] = useState<RouteObject[]>([]);
  const [headerRoutes, setHeaderRoutes] = useState<RouteObject[]>([]);
  const [hiddenRoutes, setHiddenRoutes] = useState<RouteObject[]>([]);

  const rootUrl = process.env.NEXT_PUBLIC_APP_URL;
  const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';

  const prepareMenuItem = (route: MenuItem): RouteObject => ({
    key: route.key,
    title: t(route.title!),
    path: `${rootUrl}${route.path}`,
    ...(route.icon && AllIcons[route.icon as keyof typeof AllIcons]
      ? {
          icon: React.createElement(
            AllIcons[route.icon as keyof typeof AllIcons],
            {
              color: '#191F2E !important',
              fill: '#191F2E !important',
            },
          ),
        }
      : {}),
    ...(route.routeComponents
      ? {
          routeComponents: route.routeComponents
            ?.filter(subRoute => subRoute.placement !== 'HIDDEN')
            ?.map(subRoute => prepareMenuItem(subRoute)),
        }
      : {}),
  });

  const setAllRoutes = (routes: MenuItem[]) => {
    setMenuRoutes(
      routes
        .filter(route => route.placement === 'BODY')
        .map(route => prepareMenuItem(route)),
    );
    setFooterRoutes(
      routes
        .filter(route => route.placement === 'FOOTER')
        .map(route => prepareMenuItem(route)),
    );
    setHeaderRoutes(
      routes
        .filter(route => route.placement === 'HEADER')
        .map(route => prepareMenuItem(route)),
    );
    setHiddenRoutes(
      routes
        .filter(route => route.placement === 'HIDDEN')
        .map(route => prepareMenuItem(route)),
    );
  };

  useEffect(() => {
    const fetchRoutesForUser = async () => {
      try {
        const result = await getMenuItems(
          (session.data?.user?.proxiedAs ?? session.data?.user)?.token,
          session.data?.user?.sessionToken,
        );

        if (result.data.type !== 'success') {
          throw new Error((result.data as any).errorMessage);
        }

        const {
          data: { results },
        } = result;
        const uniqMenuItems = uniqBy(results, 'key');
        uniqMenuItems.forEach(menuItem => {
          if (menuItem.routeComponents) {
            menuItem.routeComponents = uniqBy(menuItem.routeComponents, 'key');
          }
        });
        setAllRoutes(uniqMenuItems);
      } catch (error) {
        console.error('Error fetching menu items:', error);
        const result = await fetch(`${rootUrl}${basePath}/api/menu-fallback`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(session.data?.user?.permissions ?? []),
        });
        const data = await result.json();
        setAllRoutes(data.results);
      }
    };

    void fetchRoutesForUser();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [t]);

  return { menuRoutes, footerRoutes, headerRoutes, hiddenRoutes };
}
