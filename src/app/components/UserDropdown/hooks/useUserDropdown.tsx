import { useCallback, useMemo } from 'react';
import { signOut, useSession } from 'next-auth/react';
import { useTranslate } from '@tolgee/react';

import { Icon } from '~/shared/components';
import { useGetProfilePicture } from '~/entities/Employee/hooks/useGetProfilePicture';
import { SignOut } from '~/shared/components/icons/SignOut';
import { destroyNorthstarSession, stopNorthstarProxy } from '~/shared/services/auth';

type UseUserDropdownProps = {
  user: any;
};

export const useUserDropdown = ({ user }: UseUserDropdownProps) => {
  const { t } = useTranslate();
  const { data: session, update } = useSession();
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || '';

  async function handleSignOut() {
    try {
      await destroyNorthstarSession(session?.user.token, session?.user.sessionToken);
    } catch (error) {
      console.error('Error during northstar sign out:', error);
    }
    await signOut();
    window.location.href =  `${baseUrl}/`;
  }

  const goToProxy = useCallback(async () => {
    window.location.href = `${baseUrl}/admin-view/admin/proxy`;
  }, [baseUrl]);

  const handleStopProxy = useCallback(async () => {
    if (!session) {
      return;
    }

    try {
      await stopNorthstarProxy(session?.user.token, session?.user.sessionToken);
    } catch (error) {
      console.error('Error stopping northstar proxy:', error);
    }

    await update((currentSession: { user: any; }) => ({...currentSession, user: { ...currentSession?.user, proxiedAs: null } }));

    window.location.href =  `${baseUrl}/`;
  }, [session]);

  const items = useMemo(() => {
    const dropdownItems = [
      {
        label: t('common_logout'),
        onSelect: handleSignOut,
        icon: <SignOut />,
      },
    ];

    if (user.isProxying) {
      dropdownItems.unshift({
        label: t('common_stop_proxy'),
        onSelect: handleStopProxy,
        icon: <Icon.StopProxy size={18} />,
      });
    } else if (user.roles?.includes('ADMINISTRATOR')) {
      dropdownItems.unshift({
        label: t('common_proxy_as_user'),
        onSelect: goToProxy,
        icon: <Icon.StartProxy size={18} />,
      });
    }

    return dropdownItems;
  }, [t]);

  const { data } = useGetProfilePicture(user.globalId ?? '');
  const imageSrc = data ? URL.createObjectURL(data) : undefined;

  const profileInfo = {
    name: user.name ?? '',
    urlImage: imageSrc,
    zone: user.zone ?? '',
  };

  return {
    items,
    profileInfo,
  };
};
