import type { GetServerSidePropsContext, GetServerSidePropsResult } from 'next';
import { AuthSession } from '@ghq-abi/auth-client-lib';

import { getServerSessionWrapper } from '~/pages/api/auth/[...nextauth]';

function checkIfIsMobileDevice(userAgent: string) {
  return /iPhone|iPad|iPod|Android/i.test(userAgent);
}

const baseUrl = process.env.NEXT_PUBLIC_APP_URL || '';

export function withSSRSession<T extends Record<string, unknown>>(
  fn: (
    context: GetServerSidePropsContext,
    session: AuthSession<
      | {
          roles?: string[];
          permissions?: string[];
          sessionToken?: string;
        }
      | undefined
    >,
  ) => Promise<GetServerSidePropsResult<T>>,
) {
  return async <
    Q extends {
      session: AuthSession<
        | {
            roles?: string[];
            permissions?: string[];
            sessionToken?: string;
          }
        | undefined
      >;
      isMobile: boolean;
    } & T,
  >(
    context: GetServerSidePropsContext,
  ): Promise<GetServerSidePropsResult<Q>> => {
    const session = await getServerSessionWrapper(context.req, context.res);

    if (
      !session ||
      session?.error ||
      !session?.user.token ||
      !session?.user.refreshToken
    ) {
      return {
        redirect: {
          permanent: false,
          destination: '/auth/redirect',
        },
      };
    }

    if (!session?.user.sessionToken) {
      return {
        redirect: {
          permanent: false,
          destination: `${baseUrl}/`,
        },
      };
    }

    const isMobile = checkIfIsMobileDevice(
      context.req.headers['user-agent'] ?? '',
    );

    const result = await fn(context, session);

    if ('props' in result) {
      return {
        ...result,
        props: {
          ...result.props,
          session,
          isMobile,
        } as unknown as Q,
      };
    }

    return {
      ...result,
      props: {
        session,
        isMobile,
      } as unknown as Q,
    };
  };
}
