import type {
  GetServerSideProps,
  GetServerSidePropsContext,
  GetServerSidePropsResult,
} from 'next';
import { AUTH_ERRORS } from '@ghq-abi/auth-client-lib';

import { getServerSessionWrapper } from '~/pages/api/auth/[...nextauth]';

export function withSSRUnauth<T extends Record<string, unknown>>(
  fn?: GetServerSideProps<T>,
) {
  return async (
    context: GetServerSidePropsContext,
  ): Promise<GetServerSidePropsResult<T>> => {
    const session = await getServerSessionWrapper(context.req, context.res);

    const isErrorPage = context.resolvedUrl.includes('/auth/error');

    if (session?.error === AUTH_ERRORS.REFRESH_TOKEN_ERROR && isErrorPage) {
      return {
        redirect: {
          permanent: false,
          destination: '/auth/redirect',
        },
      };
    }

    if (!session?.error && session?.user) {
      return {
        redirect: { permanent: false, destination: '/' },
      };
    }

    if (!fn) {
      return {
        props: {} as T,
      };
    }

    return fn(context);
  };
}
