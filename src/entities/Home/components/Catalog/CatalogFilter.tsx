import { Bar<PERSON><PERSON><PERSON><PERSON>, ChevronDown, ListCheck } from 'react-bootstrap-icons';
import {
  Button,
  Container,
  DropdownMenu,
  ToggleGroup,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useQuery } from '@tanstack/react-query';
import { useTranslate } from '@tolgee/react';

import businessFunctionsService from '~/shared/services/businessFunctions';
import { OrderBy, SortOptions } from '~/shared/types/Sort';

import { SearchBar } from '../SearchBar';

import { CatalogFilterOptions } from './types';
import { useSession } from 'next-auth/react';

interface CatalogFilterProps {
  filters: CatalogFilterOptions;
  searchQuery: string;
  handleSearchChange: (query: string) => void;
  handleSearchKeyPress: (e: React.KeyboardEvent) => void;
  selectedFunctions: string[];
  handleLevelChange: (values: string[]) => void;
  handleFunctionToggle: (functionName: string) => void;
  handleSearchSubmit: () => void;
  toggleValues: string[];
  handleSortChange: (sortOptions: SortOptions | null) => void;
  orderBy?: OrderBy | null | undefined;
}

export const CatalogFilter = ({
  searchQuery,
  handleSearchChange,
  handleSearchKeyPress,
  selectedFunctions,
  handleLevelChange,
  handleFunctionToggle,
  handleSearchSubmit,
  toggleValues,
  handleSortChange,
  orderBy,
}: CatalogFilterProps) => {
  const { t } = useTranslate();
  const { data: session } = useSession();

  const { data: businessFunctions = [] } = useQuery({
    queryKey: ['business-functions', session?.user.token, session?.user.sessionToken],
    queryFn: () => businessFunctionsService.getBusinessFunctions(session?.user.token, session?.user.sessionToken),
    select: list => list.map(bf => bf.label),
  });

  return (
    <Container className="flex max-w-96 flex-col gap-4">
      <SearchBar
        searchQuery={searchQuery}
        onSearchChange={e => handleSearchChange?.(e)}
        onSearchKeyPress={e => handleSearchKeyPress?.(e)}
        showSearchButton
        showSortButton
        handleSearchSubmit={handleSearchSubmit}
        orderBy={orderBy}
        handleSortChange={handleSortChange}
        placeholder={t('common_search_name_calculation_method')}
      />

      <Container className="flex justify-start gap-4 relative items-center overflow-x-auto scrollbar-hide">
        <ToggleGroup.Root
          type="multiple"
          className="flex items-center gap-4"
          value={toggleValues.filter((v): v is string => v !== null)}
          onValueChange={(value: string | string[]) => {
            const arrayValue = Array.isArray(value) ? value : [value];
            handleLevelChange(arrayValue);
          }}
        >
          <ToggleGroup.Item value="KPI" className="p-2 h-8 gap-2">
            <BarChartFill />
            <Typography variant="metadata-sm-bold">
              {t('common_kpi')}
            </Typography>
          </ToggleGroup.Item>
          <ToggleGroup.Item value="PROJECT" className="p-2 h-8 gap-2">
            <ListCheck />
            <Typography variant="metadata-sm-bold">
              {t('common_project')}
            </Typography>
          </ToggleGroup.Item>
          <div className="border-l-[1px] border-[#CACDD5] h-8" />
        </ToggleGroup.Root>
        <DropdownMenu.Root modal={false}>
          <DropdownMenu.Trigger asChild>
            <Button
              variant={selectedFunctions.length > 0 ? 'light' : 'secondary'}
              border="default"
              iconRight={<ChevronDown />}
              className="p-2 h-8 gap-2"
            >
              <Typography variant="metadata-sm-bold">
                {t('common_functions')}
              </Typography>
            </Button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content className="w-56">
            {businessFunctions.map(functionName => (
              <DropdownMenu.CheckboxItem
                key={functionName}
                onSelect={e => {
                  e.preventDefault();
                  handleFunctionToggle(functionName);
                }}
                checked={selectedFunctions.includes(functionName)}
              >
                {functionName}
              </DropdownMenu.CheckboxItem>
            ))}
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      </Container>
    </Container>
  );
};
