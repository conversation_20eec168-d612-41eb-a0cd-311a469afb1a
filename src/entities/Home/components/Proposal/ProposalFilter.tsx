import { ChevronDown } from 'react-bootstrap-icons';
import {
  Button,
  Container,
  DropdownMenu,
  Skeleton,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useQuery } from '@tanstack/react-query';
import { useTranslate } from '@tolgee/react';

import proposalService from '~/shared/services/proposal';

import { SearchBar } from '../SearchBar';

import { ProposalFilterProps } from './types';
import { useSession } from 'next-auth/react';

interface ProposalFilterSelectProps {
  label: string;
  value: string;
  options: { label: string; value: string }[];
  selected: string[];
  handleToggle: (value: string, filter: string) => void;
}

const ProposalFilterSelect = ({
  label,
  value,
  options,
  selected,
  handleToggle,
}: ProposalFilterSelectProps) => {
  const getButtonVariant = () => {
    return selected.length > 0 ? 'light' : 'secondary';
  };

  return (
    <DropdownMenu.Root modal={false} key={value}>
      <DropdownMenu.Trigger asChild>
        <Button
          variant={getButtonVariant()}
          border="default"
          className="p-2 h-8 gap-2"
          iconRight={<ChevronDown />}
        >
          <Typography variant="metadata-sm-bold">{label}</Typography>
        </Button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content className="w-56">
        {options.map(option => (
          <DropdownMenu.CheckboxItem
            key={option.value}
            checked={selected.includes(option.value)}
            onSelect={e => {
              e.preventDefault();
              handleToggle(option.value, value);
            }}
          >
            {option.label}
          </DropdownMenu.CheckboxItem>
        ))}
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};

export const ProposalFilter = ({
  handleSearchChange,
  handleSearchKeyPress,
  handleToggle,
  searchQuery,
  selectedFunctions,
  selectedStatus,
  selectedZones,
  selectedSltLevel,
  selectedSltName,
  handleSearchSubmit,
}: ProposalFilterProps) => {
  const { t } = useTranslate();
  const { data: session } = useSession();

  const SLT_NAMES_LABELS: Record<0 | 1 | 2, string> = {
    0: 'SLT',
    1: 'L1',
    2: 'L2',
  };

  const { data: filters, isLoading: isFiltersLoading } = useQuery({
    queryKey: ['proposal-filters', session?.user.token, session?.user.sessionToken],
    queryFn: () => proposalService.getFilters(session?.user.token, session?.user.sessionToken),
    select: data => {
      return {
        data: {
          ...data.data,
          sltLevels: data.data.sltLevels
            .filter(
              s =>
                SLT_NAMES_LABELS[
                  s.value as unknown as keyof typeof SLT_NAMES_LABELS
                ] !== undefined,
            )
            .map(s => ({
              label:
                SLT_NAMES_LABELS[
                  s.value as unknown as keyof typeof SLT_NAMES_LABELS
                ],
              value: s.value,
            })),
        },
      };
    },
  });

  return (
    <Container className="flex flex-col gap-4">
      <SearchBar
        searchQuery={searchQuery}
        onSearchChange={handleSearchChange}
        onSearchKeyPress={handleSearchKeyPress}
        showSearchButton
        handleSearchSubmit={handleSearchSubmit}
        placeholder={t('common_search_by_name_id')}
      />
      <Container className="flex justify-start gap-4 relative items-center">
        {isFiltersLoading &&
          Array.from({ length: 5 }).map((_, index) => (
            <Skeleton key={index} className="w-32 h-8" />
          ))}
        {!isFiltersLoading && filters?.data.status && (
          <ProposalFilterSelect
            key="status"
            label={t('common_status')}
            value="status"
            options={filters.data.status}
            selected={selectedStatus}
            handleToggle={handleToggle}
          />
        )}
        {!isFiltersLoading && filters?.data.zones && (
          <ProposalFilterSelect
            key="zones"
            label={t('common_zone')}
            value="zones"
            options={filters.data.zones}
            selected={selectedZones}
            handleToggle={handleToggle}
          />
        )}

        {!isFiltersLoading && filters?.data.functions && (
          <ProposalFilterSelect
            key="businessFunctions"
            label={t('common_functions')}
            value="businessFunctions"
            options={filters.data.functions}
            selected={selectedFunctions}
            handleToggle={handleToggle}
          />
        )}

        {!isFiltersLoading && filters?.data.sltLevels && (
          <ProposalFilterSelect
            key="sltLevel"
            label={t('common_slt_level')}
            value="sltLevel"
            options={filters.data.sltLevels}
            selected={selectedSltLevel}
            handleToggle={handleToggle}
          />
        )}

        {!isFiltersLoading && filters?.data.sltNames && (
          <ProposalFilterSelect
            key="sltName"
            label={t('common_slt_name')}
            value="sltName"
            options={filters.data.sltNames}
            selected={selectedSltName}
            handleToggle={handleToggle}
          />
        )}
      </Container>
    </Container>
  );
};
