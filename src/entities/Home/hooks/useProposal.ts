import { useEffect, useMemo, useRef, useState } from 'react';
import { useQuery } from '@tanstack/react-query';

import { useUser } from '~/shared/hooks';
import { GetProposalFilters } from '~/shared/types/GetProposalFilters';
import { ProposalItemsResponse } from '~/shared/types/ProposalService';

import proposalService from '../../../shared/services/proposal';
import { ProposalFilterOptions } from '../components/Proposal/types';

const PAGE_SIZE = 9;

const mapFiltersToApiParams = (
  filters: ProposalFilterOptions,
  pageNumber: number = 1,
  pageSize: number = PAGE_SIZE,
): GetProposalFilters => {
  const apiFilters: GetProposalFilters = {
    pageNumber,
    pageSize,
    ...(filters.fuzzy_search && { search: filters.fuzzy_search }),
    ...(filters.businessFunctions?.length
      ? { businessFunctions: filters.businessFunctions }
      : {}),
    ...(filters.status?.length ? { status: filters.status } : {}),
    ...(filters.sltLevel?.length ? { sltLevel: filters.sltLevel } : {}),
    ...(filters.sltName?.length ? { sltName: filters.sltName } : {}),
    ...(filters.zones?.length ? { zones: filters.zones } : {}),
  };

  return apiFilters;
};

const hasAnyActiveFilters = (
  filters: ProposalFilterOptions,
  pageNumber: number,
) =>
  Boolean(
    filters.fuzzy_search ||
      filters.businessFunctions?.length ||
      filters.status?.length ||
      filters.sltLevel?.length ||
      filters.sltName?.length ||
      filters.zones?.length ||
      pageNumber !== 1,
  );

export const useProposal = (
  pageNumber: number = 1,
  pageSize: number = PAGE_SIZE,
  initialProposals?: ProposalItemsResponse,
  externalFilters?: ProposalFilterOptions,
) => {
  const { token, sessionToken } = useUser();
  const isInitialLoad = useRef(true);

  const effectiveFiltersForQuery = useMemo(() => {
    const {
      stagedBusinessFunctions,
      stagedStatus,
      stagedZones,
      stagedSltLevel,
      stagedSltName,
      ...rest
    } = externalFilters ?? {};
    return rest;
  }, [externalFilters]);

  const [hasFiltersBeenUsed, setHasFiltersBeenUsed] = useState(false);
  const hasActiveFilters = useMemo(
    () => hasAnyActiveFilters(externalFilters ?? {}, pageNumber),
    [externalFilters, pageNumber],
  );

  useEffect(() => {
    if (hasActiveFilters && !hasFiltersBeenUsed) {
      setHasFiltersBeenUsed(true);
    }
  }, [hasActiveFilters, hasFiltersBeenUsed]);

  const shouldUseInitialData = Boolean(
    initialProposals?.data?.length &&
      !hasFiltersBeenUsed &&
      pageNumber === 1 &&
      pageSize === PAGE_SIZE &&
      !externalFilters?.fuzzy_search &&
      !externalFilters?.businessFunctions?.length &&
      !externalFilters?.status?.length &&
      !externalFilters?.sltLevel?.length &&
      !externalFilters?.sltName?.length &&
      !externalFilters?.zones?.length,
  );

  const { data, isLoading, isError, isFetching } = useQuery({
    // eslint-disable-next-line @tanstack/query/exhaustive-deps
    queryKey: ['proposals', effectiveFiltersForQuery, pageNumber, pageSize],
    queryFn: () => {
      const apiFilters = mapFiltersToApiParams(
        effectiveFiltersForQuery,
        pageNumber,
        pageSize,
      );
      return proposalService.getProposals(token, sessionToken, apiFilters);
    },
    initialData: shouldUseInitialData ? initialProposals : undefined,
    onSuccess: () => {
      isInitialLoad.current = false;
    },
  });

  const isInitialLoading = isLoading && isInitialLoad.current;
  const isSearchLoading = isFetching && !isInitialLoad.current;

  const finalData = shouldUseInitialData ? initialProposals : data;

  return {
    proposals: finalData,
    isInitialLoading,
    isSearchLoading,
    isError,
  };
};
