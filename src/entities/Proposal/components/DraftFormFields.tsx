import { Container } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { FormikInput } from '~/shared/components/FormikInput';
import { FormikSelect } from '~/shared/components/FormikSelect';
import { FormikTextarea } from '~/shared/components/FormikTextarea';

import { WEIGHT_OPTIONS } from '../constants';

type DraftFormFieldsProps = {
  values: {
    name: string;
    calculationMethod: string;
    definition: string;
    scope: string;
    weight: number;
  };
  onChange: (e: React.ChangeEvent<any>) => void;
};

export function DraftFormFields({ values, onChange }: DraftFormFieldsProps) {
  const { t } = useTranslate();

  return (
    <Container className="flex flex-col py-4 px-2 gap-4">
      <FormikInput
        name="name"
        label={t('common_name')}
        placeholder={t('common_insert_here')}
        onChange={onChange}
        value={values.name}
      />
      <FormikTextarea
        name="calculationMethod"
        label={t('common_calculation_method_deliverables')}
        placeholder={t('common_insert_here')}
        onChange={onChange}
        value={values.calculationMethod}
      />
      <FormikTextarea
        name="definition"
        label={t('common_definition')}
        placeholder={t('common_insert_here')}
        onChange={onChange}
        value={values.definition}
      />
      <FormikTextarea
        name="scope"
        label={'Scope'}
        placeholder={t('common_insert_here')}
        onChange={onChange}
        value={values.scope}
      />
      <FormikSelect
        name="weight"
        options={WEIGHT_OPTIONS}
        label={t('common_weight')}
        placeholder={t('common_insert_here')}
        onChange={onChange}
        value={`${values.weight}`}
      />
    </Container>
  );
}
