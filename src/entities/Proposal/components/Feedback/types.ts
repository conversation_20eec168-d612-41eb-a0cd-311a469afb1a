import { useCatalogFilter } from '~/entities/Home/hooks/useCatalogFilter';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Proposal } from '~/shared/types/Proposal';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

export interface FeedbackDragProps {
  proposalStatus?: ProposalStatusEnum;
  proposalUid: string;
  targets: Target[];
  allTargets?: Target[];
  hasManagerPermission: boolean;
  onProposalUpdate?: (updatedProposal: Proposal) => void;
}

export interface FeedbackStepProps {
  targets: Target[];
  proposalUid: string;
  proposalStatus?: ProposalStatusEnum;
  hasManagerPermission: boolean;
  hasEmployeePermission: boolean;
}

export interface CatalogWithTabsProps {
  deliverables: DeliverableItem[];
  proposalTargets: Target[];
  isInitialLoading: boolean;
  isSearchLoading: boolean;
  isError: boolean;
  catalogFilterHook: ReturnType<typeof useCatalogFilter>;
  onAcceptTarget?: (targetUid: string, agree?: boolean) => void;
  acceptedTargetUids?: string[];
  isTargetLoading?: (targetUid: string) => boolean;
  showProposalTargetType?: TargetTypeEnum;
  showFeedbackTab?: boolean;
  filterAcceptedFromFeedbackTab?: boolean;
  getTargetAgreeStatus?: (targetUid: string) => boolean | null;
}

export interface CreateTargetBody {
  targets: Target[];
}

export interface UseFeedbackReturn {
  // States
  selectedTargets: Target[];
  acceptedTargetUids: string[];
  loadingTargetUids: string[];
  draggedItem: DeliverableItem | Target | null;
  isDraggingNewItem: boolean;
  isDraggingForReorder: boolean;
  isOpenDrawer: boolean;
  isEditDrawer: boolean;
  drawerDeliverable: DeliverableItem | Target | undefined;
  drawerType: 'draft' | 'target' | null;
  totalWeight: number;

  // Loading states
  isLoading: boolean;
  isLoadingDelete: boolean;
  isLoadingMergeTargets: boolean;
  isLoadingAcceptTargets: boolean;
  isLoadingAcceptAllTargets: boolean;
  isLoadingReorderTargets: boolean;

  // Handlers
  handleAcceptTarget: (targetUid: string, agree?: boolean) => void;
  handleDragStart: (event: any) => void;
  handleDragOver: (event: any) => void;
  handleDragEnd: (event: any) => void;
  handleSubmit: () => void;
  handleClearDeliverable: () => void;
  handleRemoveTargets: (targets: Target[]) => void;
  onDrawerSuccessSubmit: (proposal: any) => void;
  setIsOpenDrawer: (open: boolean) => void;
  setDrawerDeliverable: (
    deliverable: DeliverableItem | Target | undefined,
  ) => void;
  setDrawerType: (type: 'draft' | 'target' | null) => void;
  handleCreateDraft: () => void;
  handleOpenEditDrawer: (target: Target) => void;
  handleFillWithProposal: () => void;

  // Computed values
  availableDeliverables: DeliverableItem[];
  selectedFeedbackTargetUids: Set<string>;

  filterTargetsByType: (
    targetsList: Target[],
    targetType: TargetTypeEnum,
  ) => Target[];

  // Helper functions
  isTargetLoading: (targetUid: string) => boolean;
  getTargetAgreeStatus: (targetUid: string) => boolean | null;
  isTargetAgreed: (target: Target) => boolean;
  areAllTargetsAgreed: boolean;

  // Modal control
  actionModal: ReturnType<typeof useActionModal>;
}

export interface UseFeedbackParams {
  proposalUid: string;
  targets: Target[];
  allTargets: Target[];
  catalogFilterHook: ReturnType<typeof useCatalogFilter>;
  onProposalUpdate?: (updatedProposal: Proposal) => void;
}
