import { useCatalogFilter } from '~/entities/Home/hooks/useCatalogFilter';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Proposal } from '~/shared/types/Proposal';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

export interface FinalDragProps {
  proposalStatus?: ProposalStatusEnum;
  proposalUid: string;
  targets: Target[];
  allTargets?: Target[];
  onProposalUpdate?: (updatedProposal: Proposal) => void;
}

export interface FinalStepProps {
  targets: Target[];
  proposalUid: string;
  proposalStatus?: ProposalStatusEnum;
  hasManagerPermission: boolean;
  hasEmployeePermission: boolean;
}

export interface CatalogWithTabsProps {
  deliverables: DeliverableItem[];
  proposalTargets: Target[];
  isInitialLoading: boolean;
  isSearchLoading: boolean;
  isError: boolean;
  catalogFilterHook: ReturnType<typeof useCatalogFilter>;
  onAcceptTarget?: (targetUid: string) => void;
  acceptedTargetUids?: string[];
  isTargetLoading?: (targetUid: string) => boolean;
  showProposalTargetType?: TargetTypeEnum;
  showFeedbackTab?: boolean;
  showBadgesInProposalTab?: boolean;
  filterAcceptedFromFeedbackTab?: boolean;
}

export interface CreateTargetBody {
  targets: Target[];
}

export interface UseFinalReturn {
  // States
  selectedTargets: Target[];
  acceptedTargetUids: string[];
  loadingTargetUids: string[];
  draggedItem: DeliverableItem | Target | null;
  isDraggingNewItem: boolean;
  isDraggingForReorder: boolean;
  isOpenDrawer: boolean;
  drawerDeliverable: DeliverableItem | Target | undefined;
  drawerType: 'draft' | 'target' | null;
  totalWeight: number;
  isEditDrawer: boolean;

  // Loading states
  isLoading: boolean;
  isLoadingDelete: boolean;
  isLoadingMergeTargets: boolean;
  isLoadingAcceptTargets: boolean;
  isLoadingAcceptAllTargets: boolean;
  isLoadingReorderTargets: boolean;

  // Handlers
  handleAcceptTarget: (targetUid: string) => void;
  handleDragStart: (event: any) => void;
  handleDragEnd: (event: any) => void;
  handleSubmit: () => void;
  handleClearDeliverable: () => void;
  handleRemoveTargets: (targets: Target[]) => void;
  onDrawerSuccessSubmit: (proposal: any) => void;
  setIsOpenDrawer: (value: boolean) => void;
  setDrawerDeliverable: (deliverable: DeliverableItem | undefined) => void;
  setDrawerType: (type: 'draft' | 'target' | null) => void;
  handleOpenEditDrawer: (target: Target) => void;
  handleCreateDraft: () => void;
  handleFillWithProposal: () => void;
  handleFillWithFeedback: () => void;

  // Computed values
  availableDeliverables: DeliverableItem[];
  selectedFinalTargetUids: Set<string>;

  // Helper functions
  filterTargetsByType: (
    targets: Target[],
    targetType: TargetTypeEnum,
  ) => Target[];
  isTargetLoading: (targetUid: string) => boolean;
  getTargetAgreeStatus: (targetUid: string) => boolean | null;
  isTargetAgreed: (target: Target) => boolean;
  areAllTargetsAgreed: boolean;

  // Modal control
  actionModal: ReturnType<typeof useActionModal>;
}

export interface UseFinalParams {
  proposalUid: string;
  targets: Target[];
  allTargets: Target[];
  catalogFilterHook: ReturnType<typeof useCatalogFilter>;
  onProposalUpdate?: (updatedProposal: Proposal) => void;
}
