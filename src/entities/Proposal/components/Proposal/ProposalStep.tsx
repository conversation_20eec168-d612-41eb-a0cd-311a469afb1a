import React from 'react';
import { Container } from '@ghq-abi/design-system-v2';

import { ChildCard, TargetCard } from '~/shared/components/TargetCard';
import { useUser } from '~/shared/hooks';
import { Proposal } from '~/shared/types/Proposal';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { filterTargetsByType } from '../../utils/targetFilters';
import { TabEmptyState } from '../TabEmptyState';

import { ProposalDrag } from './ProposalDrag';

interface ProposalStepProps {
  targets: Target[];
  proposalUid: string;
  proposalStatus?: ProposalStatusEnum;
  hasManagerPermission?: boolean;
  hasEmployeePermission?: boolean;
  isDrawer?: boolean;
  onProposalUpdate?: (updatedProposal: Proposal) => void;
  isLoading?: boolean;
}

export function ProposalStep({
  targets,
  proposalUid,
  proposalStatus,
  hasManagerPermission,
  hasEmployeePermission,
  isDrawer,
  onProposalUpdate,
  isLoading,
}: ProposalStepProps) {
  const user = useUser();

  const proposalTargets = filterTargetsByType(targets, TargetTypeEnum.PROPOSAL);

  const isAdmin = user.isAdmin;
  const canManageProposal = hasManagerPermission || isAdmin;

  if (
    !canManageProposal &&
    (proposalStatus === ProposalStatusEnum.NOT_STARTED ||
      proposalStatus === ProposalStatusEnum.IN_PROGRESS_PROPOSAL)
  ) {
    return (
      <TabEmptyState
        title="No proposals available"
        description="There are no proposal targets to display at this time."
      />
    );
  }

  const STATUS_IN_PROGRESS_MAP = [
    ProposalStatusEnum.IN_PROGRESS_PROPOSAL,
    ProposalStatusEnum.IN_PROGRESS_FEEDBACK,
    ProposalStatusEnum.IN_PROGRESS_FINAL,
  ];

  const isShowActions =
    hasManagerPermission &&
    !STATUS_IN_PROGRESS_MAP.includes(proposalStatus as ProposalStatusEnum);

  if (
    canManageProposal &&
    (proposalStatus === ProposalStatusEnum.NOT_STARTED ||
      proposalStatus === ProposalStatusEnum.IN_PROGRESS_PROPOSAL)
  ) {
    return (
      <ProposalDrag
        proposalStatus={proposalStatus}
        proposalUid={proposalUid}
        targets={proposalTargets}
        onProposalUpdate={onProposalUpdate}
        isLoading={isLoading}
      />
    );
  }

  return (
    <Container className="flex flex-col gap-4">
      {proposalTargets.map(target => {
        if (target.children && target.children.length > 1) {
          return (
            <TargetCard
              key={target.uid}
              data={target}
              proposalStatus={proposalStatus}
              hideChildren={target.children.length <= 1}
              currentTargetType={TargetTypeEnum.PROPOSAL}
              hasManagerPermission={hasManagerPermission}
              hasEmployeePermission={hasEmployeePermission}
              isDrawer={isDrawer}
            />
          );
        }
        return (
          <ChildCard
            key={target.uid}
            target={target}
            disableDrag={true}
            hasManagerPermission={hasManagerPermission}
            hasEmployeePermission={hasEmployeePermission}
            isDrawer={isDrawer}
            showActions={isShowActions}
          />
        );
      })}
    </Container>
  );
}
