import React from 'react';
import Image from 'next/image';
import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { Button, Typography } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { Target } from '~/shared/types/Target';
import { cn } from '~/shared/utils/cn';
import { SortableDropZone } from '~/shared/components/DragAndDrop';

import dragdrop from '~/../public/img/dragdrop.gif';

export interface DraggableAreaProps {
  selectedData: Target[];
  isDraggingNewItem?: boolean;
  isDraggingForReorder?: boolean;
  children: React.ReactNode;
  actions?: Array<{
    label: string;
    onClick: () => void;
    variant: 'primary' | 'secondary';
    iconLeft?: React.ReactNode;
    iconRight?: React.ReactNode;
    disabled?: boolean;
  }>;
}

export function TargetsList({
  selectedData,
  isDraggingNewItem = false,
  isDraggingForReorder = false,
  actions,
  children,
}: DraggableAreaProps) {
  const { t } = useTranslate();
  const { isOver, setNodeRef } = useDroppable({
    id: 'selection-area',
  });

  const hasSelectedItems = selectedData.length > 0;
  const shouldShowDropZone = !hasSelectedItems || isDraggingNewItem;

  return (
    <div className="flex-1 rounded-lg p-6  flex flex-col bg-white">
      <div className="flex items-center justify-between mb-4">
        <div className="flex  flex-col">
          <Typography variant="body-md-bold">
            {t('common_selection_list')}
          </Typography>
          <Typography variant="body-sm-regular" className="text-gray-500">
            {`${t('common_selected_deliverables')} ${selectedData.length}`}
          </Typography>
        </div>
        <div className="flex gap-2">
          {actions &&
            actions.length > 0 &&
            actions.map(action => (
              <Button
                key={action.label}
                variant={action.variant}
                border="default"
                onClick={action.onClick}
                iconLeft={action.iconLeft}
                iconRight={action.iconRight}
                disabled={action.disabled}
                type="button"
              >
                {action.label}
              </Button>
            ))}
        </div>
      </div>
      <div className="flex-1 flex flex-col min-h-0">
        {shouldShowDropZone && (
          <div
            ref={setNodeRef}
            className={cn(
              'flex items-center justify-center h-full border-2 border-dashed rounded-lg p-4 mb-4 flex-shrink-0',
              { 'border-[#F2DC39]': isOver },
            )}
          >
            <div className="flex flex-col justify-center items-center h-full text-center gap-2">
              <div
                className={cn(
                  'w-14 h-14 rounded-full bg-gray-100 flex items-center justify-center',
                  {
                    'bg-[#F2DC39]': isOver,
                  },
                )}
              >
                <Image
                  src={dragdrop}
                  alt="Drag and drop"
                  width={40}
                  height={40}
                />
              </div>
              <div className="flex flex-col gap-2">
                <Typography variant="body-md-bold" className="text-gray-500">
                  {t('common_drag_and_drop_here')}
                </Typography>
                <Typography variant="body-sm-regular" className="text-gray-500">
                  {t('common_use_the_itens_on_from_the_list_in_the_left')}
                </Typography>
              </div>
            </div>
          </div>
        )}
        {hasSelectedItems && (
          <div
            ref={setNodeRef}
            className="space-y-3 flex-1 min-h-0 overflow-y-auto"
          >
            <SortableContext
              items={selectedData.map(target => target.uid || '')}
              strategy={verticalListSortingStrategy}
            >
              {React.Children.map(children, (child, index) => (
                <SortableDropZone
                  key={`drop-zone-${index}`}
                  id={`drop-zone-${index}`}
                  isActive={isDraggingForReorder}
                >
                  {child}
                </SortableDropZone>
              ))}
              {/* Final drop zone at the end */}
              <SortableDropZone
                id={`drop-zone-${React.Children.count(children)}`}
                isActive={isDraggingForReorder}
              />
            </SortableContext>
          </div>
        )}
      </div>
    </div>
  );
}
