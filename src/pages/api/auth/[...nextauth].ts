import type {
  GetServerSidePropsContext,
  NextApiRequest,
  NextApiResponse,
} from 'next';
import {
  AuthConfig,
  authServerClient,
  getServerSession,
} from '@ghq-abi/auth-client-lib/server';
import { IncomingMessage } from 'http';

import { getNorthstarSession } from '~/shared/services/auth';

const callback = (req: IncomingMessage) => {
  return async (token?: string) => {
    if (token) {
      try {
        const sessionToken = req.headers?.cookie
          ?.split(';')
          .find(c => c.trim().startsWith('northstar.session-token='))
          ?.split('=')[1];

        if (sessionToken) {
          const response = await getNorthstarSession(token, sessionToken);

          return {
            ...response.data.platform.user,
            sessionToken: response.data.sessionToken ?? sessionToken,
          };
        }
      } catch (err) {
        console.log('Error fetching Northstar session: ' + JSON.stringify(err));
      }
    }

    return {};
  };
};

export default async function auth(req: NextApiRequest, res: NextApiResponse) {
  return authServerClient(req, res, new AuthConfig(callback(req)));
}

export async function getServerSessionWrapper(
  req: GetServerSidePropsContext['req'],
  res: GetServerSidePropsContext['res'],
) {
  return getServerSession(req, res, new AuthConfig(callback(req)));
}
