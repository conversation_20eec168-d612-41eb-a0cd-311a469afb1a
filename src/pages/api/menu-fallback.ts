import type { NextApiRequest, NextApiResponse } from 'next';
import { uniq } from 'lodash';

const filterMenuItems = (permissions: string[]) => {
  const allMenuItems = [
    {
      placement: 'BODY',
      path: '/',
      title: 'common_home',
      icon: 'HouseIcon',
      key: 'D52D8FEF-C6A1-49AB-96A3-8E69526B77A0',
      privilege: null,
      routeComponents: null,
    },
    {
      placement: 'BODY',
      path: '/catchball',
      title: 'common_catchball',
      icon: 'CatchballIcon',
      key: '187933AE-0BC1-44BC-AAA6-E5D81A5A296F0',
      privilege: 'CATCHBALL_REQUEST_KPI',
      routeComponents: null,
    },
    {
      placement: 'BODY',
      path: '/dashboard/cascading-completion',
      title: 'Dashboard',
      icon: 'ChartIcon',
      key: '40B20924-E19C-4577-B3D3-63719F995458',
      privilege: 'ACCESS_TEAM_DASHBOARD',
      routeComponents: [
        {
          path: '/dashboard/cascading-completion',
          title: 'Cascading Completion',
          icon: 'ChartIcon',
          key: '41F97310-50FB-4F5E-886F-2AB7C3FFEE88',
          privilege: 'ACCESS_TEAM_DASHBOARD',
        },
        {
          path: '/dashboard/appraisal-completion',
          title: 'Appraisal Completion',
          icon: 'ChartIcon',
          key: '5F43F10D-6524-41DF-B696-93F9BF51240F',
          privilege: 'ACCESS_TEAM_DASHBOARD',
        },
        {
          path: '/dashboard/mid-year-review',
          title: 'Mid Year Review',
          icon: 'ChartIcon',
          key: 'D1BAEEF3-CEC7-4F25-A098-AB9C63DEEC7D',
          privilege: 'MYR_REQUEST_LIST',
        },
      ],
    },
    {
      placement: 'BODY',
      path: '/catalog',
      title: 'common_catalog',
      icon: 'MenuBookStarIcon',
      key: 'C9429C93-0F70-4EBB-9239-A5C21CC4F453',
      privilege: 'ACCESS_CATALOG_REQUEST',
      routeComponents: [
        {
          path: '/catalog',
          title: 'kpi_catalog',
          icon: 'MenuBookStarIcon',
          key: 'ACE4B100-D6BC-4DE3-9A65-C2DE1DCBE884',
          privilege: 'ACCESS_CATALOG_REQUEST',
        },
        {
          path: '/catalog/new-kpi-catalog/auth/redirect',
          title: 'new_kpi_catalog',
          icon: 'MenuBookStarIcon',
          key: 'E9C2EFE2-C76B-4815-8DCF-82DF8B4DF3BC',
          privilege: 'ACCESS_CATALOG_REQUEST',
        },
        {
          path: '/catalog/requests',
          title: 'common_requests',
          icon: 'MenuBookStarIcon',
          key: '41D3E016-1775-4DB7-A8AC-EB6419406C8E',
          privilege: 'ACCESS_CATALOG_REQUEST',
        },
      ],
    },
    {
      placement: 'BODY',
      path: '/shared-target',
      title: 'menu_shared_target_management',
      icon: 'LeftArrowIcon',
      key: 'F41DC0F6-67A1-446E-B3B7-3B7F94921869',
      privilege: 'SHARED_TARGETS_LIST',
      routeComponents: null,
    },
    {
      placement: 'BODY',
      path: '/my-targets/current',
      title: 'common_my_targets',
      icon: 'BullseyeIcon',
      key: '5DFF6F81-1931-4C05-A21B-D0F61A0ACA4D',
      privilege: 'ACCESS_TARGETS',
      routeComponents: null,
    },
    {
      placement: 'BODY',
      path: '/my-team/cascading',
      title: 'common_my_team',
      icon: 'PeopleIcon',
      key: '6D48D06D-0547-4AE2-9236-24A98961BF8C',
      privilege: 'ACCESS_TEAM_TARGETS',
      routeComponents: [
        {
          path: '/my-team/cascading',
          title: 'common_cascading',
          icon: 'PeopleIcon',
          key: '4BD24695-FA8A-4505-8188-4D898C533257',
          privilege: 'ACCESS_TEAM_TARGETS_CASCADING',
        },
        {
          path: '/my-team/trackmonitoring',
          title: 'common_tracking_monitoring',
          icon: 'PeopleIcon',
          key: '47250606-2C81-4552-8144-B5B6C4D47512',
          privilege: 'ACCESS_TEAM_TARGETS_CURRENT',
        },
        {
          path: '/my-team/appraisal',
          title: 'common_appraisal',
          icon: 'PeopleIcon',
          key: 'E5E573AA-7834-4EC7-935D-0BB4AE2F8E4F',
          privilege: 'ACCESS_TEAM_TARGETS_CURRENT',
        },
      ],
    },
    {
      placement: 'BODY',
      path: '/my-scope/cascading',
      title: 'menu_my_scope',
      icon: 'BuildingIcon',
      key: '65602EC2-C49E-4DD6-AD8B-9AC892593458',
      privilege: 'ACCESS_MY_SCOPE',
      routeComponents: [
        {
          path: '/my-scope/cascading',
          title: 'common_cascading',
          icon: 'BuildingIcon',
          key: '2BC73130-EA81-4843-8EC6-A3D67770F6C0',
          privilege: 'ACCESS_MY_SCOPE_CASCADING',
        },
        {
          path: '/my-scope/trackmonitoring',
          title: 'common_tracking_monitoring',
          icon: 'BuildingIcon',
          key: 'BF747752-35E0-4EE1-8E48-72C2F1962FCA',
          privilege: 'ACCESS_MY_SCOPE_CURRENT',
        },
        {
          path: '/my-scope/appraisal',
          title: 'common_appraisal',
          icon: 'BuildingIcon',
          key: '07220BDF-CC31-4F34-A9EC-1417833FBC1B',
          privilege: 'ACCESS_MY_SCOPE_CURRENT',
        },
      ],
    },
    {
      placement: 'BODY',
      path: '/my-organization',
      title: 'menu_my_organization',
      icon: 'DiagramIcon',
      key: '0F6391A1-DCE0-406E-9234-BAF9660A4108',
      privilege: 'ACCESS_MYORGANIZATION',
      routeComponents: null,
    },
    {
      placement: 'BODY',
      path: '/rewards/current',
      title: 'menu_rewards',
      icon: 'MenuMoneyBagIcon',
      key: '568D1A6D-FF90-4692-8FF0-0F276F428CB3',
      privilege: 'ACCESS_REWARDS',
      routeComponents: [
        {
          path: '/rewards/current',
          title: 'menu_rewards',
          icon: 'MenuMoneyBagIcon',
          key: 'C680D8A0-75BC-4698-A44A-69B27866CDD1',
          privilege: 'ACCESS_REWARDS',
        },
        {
          path: '/rewards/bonus-calculation',
          title: 'common_bonus_calculation_module',
          icon: 'MenuMoneyBagIcon',
          key: 'B2A11AC5-B0C6-46E1-88BF-7865D368CA59',
          privilege: 'ACCESS_BONUS_CALCULATIONS',
        },
        {
          path: '/rewards/band-multiplier',
          title: 'Band Multiplier',
          icon: 'MenuMoneyBagIcon',
          key: 'C39CB6F5-C02D-409F-88F6-64422537A376',
          privilege: 'ACCESS_BAND_MULTIPLIERS',
        },
        {
          path: '/rewards/individual-incentive',
          title: 'Individual Incentive',
          icon: 'MenuMoneyBagIcon',
          key: '70002A6B-C7E7-45CE-ACCF-7EDC134228AF',
          privilege: 'ACCESS_INDIVIDUAL_INCENTIVES',
        },
        {
          path: '/rewards/file-group',
          title: 'File Group',
          icon: 'MenuMoneyBagIcon',
          key: '6A14D8E7-1F9E-4799-8911-97B922035967',
          privilege: 'ACCESS_FILE_GROUPS',
        },
        {
          path: '/rewards/bonus-election-periods',
          title: 'Bonus Election Period',
          icon: 'MenuMoneyBagIcon',
          key: '17F7C2A2-EDB3-4CD3-BD04-823DD2F01EAB',
          privilege: 'ACCESS_BONUS_ELECTIONS',
        },
      ],
    },
    {
      placement: 'BODY',
      path: '/admin-view/target-document',
      title: 'menu_admin_view',
      icon: 'PersonGearIcon',
      key: '1FC30308-2431-41DD-87C4-BE99481F00A6',
      privilege: 'ACCESS_MGMT',
      routeComponents: [
        {
          path: '/admin-view/admin/employee',
          title: 'menu_mass_creation',
          icon: 'PersonGearIcon',
          key: 'D34D0B5C-38C7-4883-BF0F-0E56342B7784',
          privilege: 'ACCESS_MGMT',
        },
        {
          path: '/admin-view/catalog',
          title: 'menu_catalog_management',
          icon: 'PersonGearIcon',
          key: '114E5F8B-5728-4466-AF43-B0A6BB0FB47A',
          privilege: 'ACCESS_MGMT',
        },
        {
          path: '/admin-view/target-document',
          title: 'menu_target_document',
          icon: 'PersonGearIcon',
          key: '92F10C0E-BDFC-4B17-9376-8AE4C363161E',
          privilege: 'ACCESS_MGMT',
        },
        {
          path: '/admin-view/communications',
          title: 'common_communication_module',
          icon: 'PersonGearIcon',
          key: 'F32A8C03-8D62-49F0-BBE1-D6AE0A335A56',
          privilege: 'ACCESS_MGMT',
        },
        {
          path: '/admin-view/helpcenter',
          title: 'menu_faq',
          icon: 'PersonGearIcon',
          key: '3C8D2585-38E3-4154-B9D6-04689194FE38',
          privilege: 'ACCESS_MGMT',
        },
        {
          path: '/admin-view/entities-targets',
          title: 'menu_entities_targets',
          icon: 'PersonGearIcon',
          key: '0EE7145D-B260-4DA9-9FCE-ED734DB1CD5F',
          privilege: 'ACCESS_MGMT',
        },
        {
          path: '/admin-view/performance-entity',
          title: 'performance_entity',
          icon: 'PersonGearIcon',
          key: '6BDD90AD-15CC-420C-817B-506CAAE777C9',
          privilege: 'ACCESS_MGMT',
        },
        {
          path: '/admin-view/sop',
          title: 'common_sop_module',
          icon: 'PersonGearIcon',
          key: '8E75211D-8C01-4714-986C-7D87EC80DC4E',
          privilege: 'ACCESS_MGMT',
        },
        {
          path: '/admin-view/management/user',
          title: 'common_user_management_module',
          icon: 'PersonGearIcon',
          key: 'F2918F41-62FB-47EB-9D3E-B675ECEBE134',
          privilege: 'ACCESS_MGMT',
        },
        {
          path: '/admin-view/management/supervisory',
          title: 'common_supervisory_module',
          icon: 'PersonGearIcon',
          key: '7CE4FF2F-6199-477E-9E1A-6144ACB5B37D',
          privilege: 'CASCADING_DEPTH_SCREEN',
        },
      ],
    },
    {
      placement: 'BODY',
      path: '/reporting-module',
      title: 'common_reporting_module',
      icon: 'ClipboardCheckLargeIcon',
      key: '7D86DE7F-AC31-4076-AEB1-061ECA7BF081',
      privilege: 'REPORT_LIST',
      routeComponents: null,
    },
    {
      placement: 'BODY',
      path: '/download-center',
      title: 'common_download_center',
      icon: 'DownloadIcon',
      key: 'E85C8360-F1ED-41F9-9807-2213097A0DAC',
      privilege: 'ACCESS_DOWNLOAD_CENTER',
      routeComponents: null,
    },
    {
      placement: 'FOOTER',
      path: 'https://anheuserbuschinbev.sharepoint.com/sites/GlobalTargetSettingandCascading?e=1%3A22b26e28a1ef4158bfd9ae5c3c6f27bd&CID=d027784b-91a9-cd3f-baa2-f51d287c0cf0&OR=Teams-HL&CT=1733340728939&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiIxNDE1LzI0MTAyMDAxMzE4IiwiSGFzRmVkZXJhdGVkVXNlciI6dHJ1ZX0%3D',
      title: 'menu_faq',
      icon: 'QuestionOutlineIcon',
      key: '84C3FF12-F292-472F-B1BC-F70089F1AE91',
      privilege: null,
      routeComponents: null,
    },
    {
      placement: 'HEADER',
      path: '/notifications',
      title: 'common_notifications',
      icon: 'BellIcon',
      key: 'F9E9AB92-FD00-43AA-8137-1AD7724944F0',
      privilege: null,
      routeComponents: null,
    },
  ];

  const validateItemPermission = (
    item: { privilege: string | null },
    permissions: string[],
  ) => !item.privilege || permissions.includes(item.privilege);

  return allMenuItems
    .map(item => {
      if (validateItemPermission(item, permissions)) {
        return {
          ...item,
          routeComponents: item.routeComponents?.filter(subItem =>
            validateItemPermission(subItem, permissions),
          ),
        };
      }
    })
    .filter(item => item);
};

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const permissions = uniq<string>(req.body) || [];
  return res.status(200).json({
    type: 'success',
    title: 'Success!',
    results: filterMenuItems(permissions),
  });
}
