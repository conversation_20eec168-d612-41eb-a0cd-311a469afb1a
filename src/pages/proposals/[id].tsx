import { useRouter } from 'next/router';

import { withSSRSession } from '~/app/hocs';
import * as Proposal from '~/entities/Proposal/ui/Page';
import { useGetProposal } from '~/shared/hooks/useGetProposal';

export default function ProposalPage() {
  const router = useRouter();
  const { id } = router.query;

  const proposal = useGetProposal(id);

  if (!proposal) {
    return null;
  }

  return (
    <Proposal.Page proposal={proposal?.data} isLoading={proposal?.isLoading} />
  );
}

export const getServerSideProps = withSSRSession(async context => {
  const { id } = context.params!;

  if (!id) {
    return {
      notFound: true,
    };
  }

  return {
    props: {},
  };
});
