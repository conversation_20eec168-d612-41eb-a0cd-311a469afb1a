import { withSSRSession } from '~/app/hocs';
import * as Home from '~/entities/Home';
import { DeliverableType } from '~/entities/Home/components/Catalog/types';
import deliverablesService from '~/shared/services/deliverables';
import employeeService from '~/shared/services/employee';
import proposalService from '~/shared/services/proposal';
import { DeliverableItemsResponse } from '~/shared/types/DeliverablesService';
import { ProposalItemsResponse } from '~/shared/types/ProposalService';
import { checkIfUserCanEdit } from '~/shared/utils/checkUserPermissions';

interface IndexProps {
  initialKpis: DeliverableItemsResponse;
  initialProposals: ProposalItemsResponse;
}

export default function Index({ initialKpis, initialProposals }: IndexProps) {
  return (
    <Home.Page initialKpis={initialKpis} initialProposals={initialProposals} />
  );
}

export const getServerSideProps = withSSRSession(async (_context, session) => {
  try {
    if (!session?.user.sessionToken || !session?.user.token) {
      throw new Error('User is not authenticated');
    }

    if (
      checkIfUserCanEdit(session.user.proxiedAs ?? (session.user as any)) ===
      false
    ) {
      const employeeProposals = await employeeService.getEmployeeProposals(
        (session.user.proxiedAs ?? (session.user as any)).token,
        (session.user.proxiedAs ?? (session.user as any)).sessionToken,
      );

      return {
        redirect: {
          destination: `/proposals/${
            employeeProposals.proposals[0]?.uid || ''
          }`,
          permanent: false,
        },
      };
    }

    const initialKpis = await deliverablesService.getDeliverables(
      session.user.token,
      session.user.sessionToken,
      {
        pageNumber: 1,
        pageSize: 100,
        deliverableTypes: [DeliverableType.KPI, DeliverableType.PROJECT],
      },
    );

    const initialProposals = await proposalService.getProposals(
      session.user.token,
      session.user.sessionToken,
      {
        pageNumber: 1,
        pageSize: 9,
      },
    );

    return {
      props: {
        initialKpis,
        initialProposals,
      },
    };
  } catch (error) {
    console.error('Error fetching initial data:', error);

    return {
      props: {
        initialKpis: {
          data: [],
          pageNumber: 1,
          pageSize: 100,
          totalRecords: 0,
        },
        initialProposals: {
          data: [],
          pageNumber: 1,
          pageSize: 9,
          totalRecords: 0,
        },
      },
    };
  }
});
