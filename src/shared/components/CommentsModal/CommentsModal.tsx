import React, {
  type ChangeEvent,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { SendFill, Trash, XLg } from 'react-bootstrap-icons';
import { Button, Drawer, Input, Typography } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';
import { format } from 'date-fns';
import { useSession } from 'next-auth/react';

import { Avatar } from '~/shared/components/Avatar';
import { Comment } from '~/shared/components/icons/Comment';
import proposalCommentsService from '~/shared/services/comments';
import targetCommentsService from '~/shared/services/targetComments';
import {
  Author,
  Body,
  CommentContent,
  Item,
  ItemHeader,
  List,
  Timestamp,
} from './styles';
import type {
  CommentData,
  CommentsModalProps,
  CommentsModalRefApi,
} from './types';

export const CommentsModal = forwardRef<
  CommentsModalRefApi,
  CommentsModalProps
>(({ parentId, commentType = 'proposal', title, onClose }, ref) => {
  const { t } = useTranslate();
  const { data: session } = useSession();
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [comments, setComments] = useState<CommentData[]>([]);
  const [currentParentId, setCurrentParentId] = useState(parentId); // Track current parentId

  const modalTitle = title || t('comments_title');

  useEffect(() => {
    if (parentId && parentId !== currentParentId) {
      setCurrentParentId(parentId);
    }
  }, [parentId, currentParentId]);

  const loadComments = async (dynamicParentId?: string) => {
    const targetParentId = dynamicParentId || currentParentId;
    setIsLoading(true);
    try {
      let response;
      if (commentType === 'target') {
        response = await targetCommentsService.getComments(session?.user.token, session?.user.sessionToken, targetParentId);
      } else {
        response = await proposalCommentsService.getComments(session?.user.token, session?.user.sessionToken, targetParentId);
      }
      const commentsData = response?.data || [];
      setComments(Array.isArray(commentsData) ? commentsData : []);
    } catch (error) {
      console.error(`Failed to load ${commentType} comments:`, error);
      setComments([]);
    } finally {
      setIsLoading(false);
    }
  };

  useImperativeHandle(ref, () => ({
    open: (dynamicParentId?: string) => {
      if (dynamicParentId) {
        setCurrentParentId(dynamicParentId);
        void loadComments(dynamicParentId);
      } else {
        void loadComments();
      }
      setIsOpen(true);
    },
    close: () => setIsOpen(false),
    refresh: loadComments,
    loadComments,
  }));

  async function handleAdd() {
    if (!session?.user.globalId) {
      console.error(`User globalId is required for ${commentType} comments`);
      return;
    }

    setIsSubmitting(true);
    try {
      if (commentType === 'target') {
        await targetCommentsService.createComment(session?.user.token, session?.user.sessionToken, currentParentId, {
          comment: message,
        });
      } else {
        await proposalCommentsService.createComment(currentParentId, {
          comment: message,
        }, session?.user.token, session?.user.sessionToken);
      }
      setMessage('');
      await loadComments();
    } catch (error) {
      console.error(`Failed to create ${commentType} comment:`, error);
    } finally {
      setIsSubmitting(false);
    }
  }

  async function handleRemove(id: string) {
    try {
      if (commentType === 'target') {
        await targetCommentsService.deleteComment(id, session?.user.token, session?.user.sessionToken);
      } else {
        await proposalCommentsService.deleteComment(id, session?.user.token, session?.user.sessionToken);
      }
      await loadComments();
    } catch (error) {
      console.error(`Failed to delete ${commentType} comment:`, error);
    }
  }

  return (
    <Drawer.Root
      direction="right"
      open={isOpen}
      onOpenChange={open =>
        open ? setIsOpen(true) : (setIsOpen(false), onClose?.())
      }
    >
      <Drawer.Content
        className="w-full max-w-xl h-screen flex flex-col bg-white"
        style={{ zIndex: 9999 }}
      >
        <Drawer.Title hidden />
        <Drawer.Description hidden />

        <div className="px-4 py-3 flex items-center justify-between border-b border-gray-200">
          <div className="flex items-center gap-2">
            <Comment size={18} color="#374151" />
            <Typography
              variant="body-md-regular"
              className="font-medium text-gray-900"
            >
              {modalTitle}
            </Typography>
          </div>
          <Button
            variant="tertiary"
            onClick={() => (setIsOpen(false), onClose?.())}
            aria-label="Close comments"
            className="p-1 hover:bg-gray-100 rounded"
          >
            <XLg size={16} className="text-gray-500" />
          </Button>
        </div>

        <div className="px-4 py-0 overflow-y-auto flex-1">
          {isLoading ? (
            <div className="flex justify-center items-center h-32">
              <Typography variant="body-sm-regular" color="light">
                {t('comments_loading')}
              </Typography>
            </div>
          ) : (
            <List>
              {!comments || comments.length === 0 ? (
                <div className="p-4">
                  <Typography variant="body-sm-regular" color="light">
                    {t('comments_empty_state')}
                  </Typography>
                </div>
              ) : (
                comments.map(comment => (
                  <Item key={comment.id}>
                    <Avatar
                      name={comment.author.name || 'Anonymous'}
                      globalId={comment.author.globalId}
                      size={32}
                      fontScale={0.4}
                      className="mt-1"
                    />
                    <CommentContent>
                      <ItemHeader>
                        <Author>{comment.author.name || 'Anonymous'}</Author>
                        <div className="flex items-center gap-1">
                          <Timestamp>
                            {format(new Date(comment.createdAt), 'HH:mm dd/MM')}
                          </Timestamp>
                          {(() => {
                            const userGlobalId = session?.user.globalId;
                            const commentAuthorId = comment.author.globalId;
                            const shouldShowDelete =
                              String(userGlobalId) === String(commentAuthorId);

                            return shouldShowDelete ? (
                              <Button
                                variant="tertiary"
                                onClick={() => handleRemove(comment.id)}
                                className="p-1 hover:bg-gray-100 rounded text-gray-500"
                              >
                                <Trash size={14} />
                              </Button>
                            ) : null;
                          })()}
                        </div>
                      </ItemHeader>
                      <Body>{comment.message}</Body>
                    </CommentContent>
                  </Item>
                ))
              )}
            </List>
          )}
        </div>

        <Drawer.Footer className="border-t border-gray-200 bg-white p-4">
          <div className="flex items-center gap-2 w-full">
            <div className="flex-1">
              <Input
                value={message}
                onChange={(e: ChangeEvent<HTMLInputElement>) =>
                  setMessage(e.target.value)
                }
                placeholder={t('comments_type_message')}
                maxLength={1000}
                disabled={isSubmitting}
                className="border-gray-300 focus:border-gray-400 focus:ring-0 rounded-md text-sm"
              />
            </div>
            <Button
              variant="primary"
              onClick={handleAdd}
              disabled={!message.trim() || isSubmitting}
              aria-label="Send comment"
              className="px-3 py-2 bg-yellow-400 hover:bg-yellow-500 text-black rounded-md disabled:opacity-50 disabled:cursor-not-allowed min-w-[40px] flex items-center justify-center"
            >
              {isSubmitting ? (
                <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin" />
              ) : (
                <SendFill size={16} className="text-black" />
              )}
            </Button>
          </div>
        </Drawer.Footer>
      </Drawer.Content>
    </Drawer.Root>
  );
});

CommentsModal.displayName = 'CommentsModal';

export default CommentsModal;
