import React from 'react';
import { useDroppable } from '@dnd-kit/core';

import { cn } from '~/shared/utils/cn';

export interface SortableDropZoneProps {
  id: string;
  isActive?: boolean;
  children?: React.ReactNode;
  className?: string;
}

export function SortableDropZone({
  id,
  isActive = false,
  children,
  className,
}: SortableDropZoneProps) {
  const { setNodeRef, isOver } = useDroppable({
    id,
    data: {
      type: 'sortable-drop-zone',
    },
  });

  if (!isActive) {
    return children ? <div>{children}</div> : null;
  }

  return (
    <>
      {children && <div>{children}</div>}
      <div
        ref={setNodeRef}
        className={cn(
          'h-12 mx-2 my-2 rounded-lg border-2 border-dashed transition-all duration-200',
          isOver
            ? 'bg-yellow-200 border-yellow-400 shadow-lg scale-105'
            : 'bg-yellow-100 border-yellow-300',
          'opacity-90 hover:opacity-100 hover:scale-102',
          className,
        )}
        style={{
          minHeight: '48px',
        }}
      >
        <div className="h-full w-full flex items-center justify-center">
          <div className="text-sm text-yellow-700 font-medium opacity-80">
            Drop here to reorder
          </div>
        </div>
      </div>
    </>
  );
}
