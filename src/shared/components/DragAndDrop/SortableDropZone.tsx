import React from 'react';
import { useDroppable } from '@dnd-kit/core';

import { cn } from '~/shared/utils/cn';

export interface SortableDropZoneProps {
  id: string;
  isActive?: boolean;
  children?: React.ReactNode;
  className?: string;
}

export function SortableDropZone({
  id,
  isActive = false,
  children,
  className,
}: SortableDropZoneProps) {
  const { setNodeRef, isOver } = useDroppable({
    id,
    data: {
      type: 'sortable-drop-zone',
    },
  });

  if (!isActive) {
    return children ? <div>{children}</div> : null;
  }

  return (
    <>
      {children && <div>{children}</div>}
      <div
        ref={setNodeRef}
        className={cn(
          'h-4 mx-2 my-1 rounded-md border-2 border-dashed transition-all duration-200',
          isOver
            ? 'bg-yellow-200 border-yellow-400 shadow-md'
            : 'bg-yellow-100 border-yellow-300',
          'opacity-80 hover:opacity-100',
          className,
        )}
        style={{
          minHeight: '16px',
        }}
      >
        <div className="h-full w-full flex items-center justify-center">
          <div className="text-xs text-yellow-600 font-medium opacity-70">
            Drop here to reorder
          </div>
        </div>
      </div>
    </>
  );
}
