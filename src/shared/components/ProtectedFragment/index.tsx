import React from 'react';
import { useSession } from 'next-auth/react';

import { ProtectedFragmentProps } from './types';

export function ProtectedFragment({
  children,
  requiredRoles = [],
  fallback = null,
}: ProtectedFragmentProps) {
  const session = useSession();

  const userRoles =
    session.data?.user?.proxiedAs?.roles ?? session.data?.user?.roles;

  const hasRequiredRoles = requiredRoles.every(role =>
    userRoles.includes(role),
  );

  if (hasRequiredRoles) {
    return <>{children}</>;
  }

  return <>{fallback}</>;
}
