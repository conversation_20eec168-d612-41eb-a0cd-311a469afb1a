import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';

import { ProtectedRouteProps } from './types';

export function ProtectedRoute({
  children,
  requiredRoles = [],
  fallbackPath = '/',
}: ProtectedRouteProps) {
  const session = useSession();

  const userRoles =
    session.data?.user?.proxiedAs?.roles ?? session.data?.user?.roles;

  const hasRequiredRoles = requiredRoles.every(role =>
    userRoles.includes(role),
  );

  const router = useRouter();

  useEffect(() => {
    if (!hasRequiredRoles) {
      void router.replace(fallbackPath);
    }
  }, [session, router, fallbackPath]);

  if (!hasRequiredRoles) {
    return null;
  }

  return <>{children}</>;
}
