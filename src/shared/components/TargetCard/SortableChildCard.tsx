import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import { Target } from '~/shared/types/Target';

import { ChildCard, ChildCardProps } from './ChildCard';

interface SortableChildCardProps extends Omit<ChildCardProps, 'target'> {
  target: Target;
  enableSortable?: boolean;
}

export function SortableChildCard({
  target,
  enableSortable = false,
  disableDrag,
  ...props
}: SortableChildCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: target?.uid || 'sortable-child-card',
    disabled: !enableSortable || disableDrag,
    data: {
      type: 'target',
      data: target,
      sortable: enableSortable, // Add flag to identify sortable drags
    },
  });

  const style = enableSortable
    ? {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.8 : 1,
        zIndex: isDragging ? 1000 : 1,
      }
    : undefined;

  if (enableSortable) {
    return (
      <div
        ref={setNodeRef}
        style={style}
        {...attributes}
        {...listeners}
        className={isDragging ? 'cursor-grabbing' : 'cursor-grab'}
      >
        <ChildCard
          target={target}
          {...props}
          disableDrag={disableDrag || true}
        />
      </div>
    );
  }

  return <ChildCard target={target} {...props} disableDrag={disableDrag} />;
}
