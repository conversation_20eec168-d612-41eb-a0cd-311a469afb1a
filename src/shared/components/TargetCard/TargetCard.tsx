import { Badge, Card, Container } from '@ghq-abi/design-system-v2';

import { CatalogItemDetailCardDeliverables } from '~/shared/components/CatalogItemDetailCard';
import { DeliverableTypeEnum } from '~/shared/types/DeliverableType';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';
import {
  getProjectDeliverables,
  getTargetCardFooterText,
  shouldDisplayDeliverables,
} from '~/shared/utils/getTargetCardFooterText';

import { TargetFooter } from './components/TargetFooter';
import { ChildCard } from './ChildCard';
import { ParentCard } from './ParentCard';
import { TargetCardProps } from './types';

export function TargetCard({
  data,
  proposalStatus,
  currentTargetType,
  hideChildren,
  onEditTarget,
  onRemoveActionClick,
  hasManagerPermission,
  hasEmployeePermission,
  isDrawer,
  isOnDroppableArea,
  isOnCatalogWithTabs = false,
  onAcceptTarget,
  getTargetAgreeStatus,
  isTargetAccepted,
  isTargetLoading,
  showBadgesInProposalTab = false,
  isTargetAgreed,
}: TargetCardProps) {
  const footerText = getTargetCardFooterText(
    proposalStatus,
    currentTargetType,
    data,
  );

  const filteredChildren = currentTargetType
    ? data.children?.filter(child =>
        child.targetTypes?.some(
          targetType => targetType.type === currentTargetType,
        ),
      )
    : data.children;

  const renderFooterBadge = () => {
    if (!footerText) {
      return null;
    }

    const isAgree = footerText === 'I Agree';
    const isDisagree = footerText === "Don't agree";

    if (isAgree) {
      return (
        <Badge className="border border-green-500 text-green-500 bg-transparent">
          {footerText}
        </Badge>
      );
    }

    if (isDisagree) {
      return (
        <Badge className="border border-red-500 text-red-500 bg-transparent">
          {footerText}
        </Badge>
      );
    }

    return null;
  };

  const renderFooterContent = () => {
    if (isOnCatalogWithTabs) {
      return (
        <TargetFooter
          target={data}
          isOnCatalogWithTabs={isOnCatalogWithTabs}
          showBadgesInProposalTab={showBadgesInProposalTab}
          onAcceptTarget={onAcceptTarget}
          agreeStatus={data.uid ? getTargetAgreeStatus?.(data.uid) : null}
          isTargetAccepted={isTargetAccepted}
          isTargetLoading={isTargetLoading}
        />
      );
    }

    return (
      <Container className="flex justify-end">{renderFooterBadge()}</Container>
    );
  };

  return (
    <Card.Root round="md">
      <ParentCard
        data={data}
        onRemoveActionClick={onRemoveActionClick}
        isDrawer={isDrawer}
        isOnCatalogWithTabs={isOnCatalogWithTabs}
        isTargetAgreed={isTargetAgreed ? isTargetAgreed(data) : false}
      />
      {shouldDisplayDeliverables(data) && (
        <Card.Content className="px-4 pb-2">
          <CatalogItemDetailCardDeliverables
            info="DELIVERABLES"
            content={getProjectDeliverables(data).map(d => ({
              ...d,
              type: DeliverableTypeEnum.KPI,
              name: d.name || 'Unnamed Deliverable',
            }))}
            type={DeliverableTypeEnum.PROJECT}
            showHeaderIcon={false}
            onCopy={() => {}}
            className="border-0 p-0"
          />
        </Card.Content>
      )}
      {!hideChildren && (
        <Card.Content className="flex flex-col gap-4">
          {filteredChildren?.map(child => {
            const shouldDisableChildActions = () => {
              if (isTargetAgreed && isTargetAgreed(child)) {
                return true;
              }

              const childTargetTypes =
                child.targetTypes?.map(tt => tt.type) || [];

              if (currentTargetType === TargetTypeEnum.FEEDBACK) {
                return childTargetTypes.includes(TargetTypeEnum.PROPOSAL);
              } else if (currentTargetType === TargetTypeEnum.FINAL) {
                return (
                  childTargetTypes.includes(TargetTypeEnum.PROPOSAL) ||
                  childTargetTypes.includes(TargetTypeEnum.FEEDBACK)
                );
              }

              return false;
            };

            return (
              <ChildCard
                key={child.uid}
                target={child}
                disableDrag={true}
                onEditTarget={onEditTarget}
                onRemoveActionClick={onRemoveActionClick}
                hasManagerPermission={hasManagerPermission}
                hasEmployeePermission={hasEmployeePermission}
                isDrawer={isDrawer}
                showActions={isOnDroppableArea}
                isOnCatalogWithTabs={isOnCatalogWithTabs}
                isInsideTargetCard={true}
                disableChildActions={shouldDisableChildActions()}
                getTargetAgreeStatus={getTargetAgreeStatus}
              />
            );
          })}
          {renderFooterContent()}
        </Card.Content>
      )}
    </Card.Root>
  );
}
