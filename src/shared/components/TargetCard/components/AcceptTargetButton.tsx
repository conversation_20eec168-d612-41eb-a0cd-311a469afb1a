import { CheckCircle, XCircle } from 'react-bootstrap-icons';
import { Button, Typography } from '@ghq-abi/design-system-v2';

interface AcceptTargetButtonProps {
  agreeStatus?: boolean | null;
  isLoading?: boolean;
  onAccept: (agree?: boolean) => void;
  isAccepted?: boolean;
}

export function AcceptTargetButton({
  agreeStatus,
  isLoading,
  onAccept,
  isAccepted: _isAccepted,
}: AcceptTargetButtonProps) {
  const shouldHideButtons = agreeStatus !== null && agreeStatus !== undefined;
  if (shouldHideButtons) {
    return null;
  }

  return (
    <div className="flex gap-2">
      <Button
        variant="secondary"
        size="sm"
        border="default"
        round="md"
        iconLeft={<CheckCircle />}
        onClick={() => onAccept(true)}
        disabled={isLoading}
        isLoading={isLoading}
      >
        <Typography variant="body-sm-medium" className="font-semibold">
          I Agree
        </Typography>
      </Button>
      <Button
        variant="secondary"
        size="sm"
        border="default"
        round="md"
        iconLeft={<XCircle />}
        onClick={() => onAccept(false)}
        disabled={isLoading}
        isLoading={isLoading}
      >
        <Typography variant="body-sm-medium" className="font-semibold">
          Don&apos;t Agree
        </Typography>
      </Button>
    </div>
  );
}
