import { Target } from '~/shared/types/Target';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

interface UseTargetActionsProps {
  target: Target;
  isOnCatalogWithTabs?: boolean;
  showBadgesInProposalTab?: boolean;
  onAcceptTarget?: (targetUid: string, agree?: boolean) => void;
  agreeStatus?: boolean | null;
  isTargetAccepted?: (targetUid: string) => boolean;
  isTargetLoading?: (targetUid: string) => boolean;
}

export function useTargetActions({
  target,
  isOnCatalogWithTabs = false,
  showBadgesInProposalTab: _showBadgesInProposalTab = false,
  onAcceptTarget,
  agreeStatus,
  isTargetAccepted,
  isTargetLoading,
}: UseTargetActionsProps) {
  const hasProposal = target.targetTypes?.some(
    t => t.type === TargetTypeEnum.PROPOSAL,
  );
  const hasFeedback = target.targetTypes?.some(
    t => t.type === TargetTypeEnum.FEEDBACK,
  );
  const hasFinal = target.targetTypes?.some(
    t => t.type === TargetTypeEnum.FINAL,
  );

  const isOnlyProposal = hasProposal && !hasFeedback && !hasFinal;
  const isProposalFeedback = hasProposal && hasFeedback && !hasFinal;
  const isAllThreeTypes = hasProposal && hasFeedback && hasFinal;

  const shouldShowIAgree = isProposalFeedback || isAllThreeTypes;
  const shouldShowDontAgree = isOnlyProposal;

  const hasAgreeStatus = agreeStatus !== null && agreeStatus !== undefined;

  const shouldShowBadges = isOnCatalogWithTabs && target.uid && hasAgreeStatus;

  const shouldShowAcceptButton =
    isOnCatalogWithTabs && target.uid && onAcceptTarget && !hasAgreeStatus;
  const isAccepted = target.uid ? isTargetAccepted?.(target.uid) : false;
  const isLoading = target.uid ? isTargetLoading?.(target.uid) || false : false;

  const handleAcceptTarget = (agree?: boolean) => {
    if (target.uid && onAcceptTarget) {
      onAcceptTarget(target.uid, agree);
    }
  };

  return {
    shouldShowIAgree,
    shouldShowDontAgree,
    shouldShowBadges,
    shouldShowAcceptButton,
    agreeStatus,
    hasAgreeStatus,
    isAccepted,
    isLoading,
    handleAcceptTarget,
  };
}
