import { GetServerSidePropsContext, GetServerSidePropsResult } from 'next';
import { AuthSession } from '@ghq-abi/auth-client-lib';

import { withSSRSession } from '~/app/hocs/withSSRSession';

export function withAdminRoute<
  P extends Record<string, unknown> = Record<string, unknown>,
>(
  getServerSidePropsFunc?: (
    context: GetServerSidePropsContext,
    session: AuthSession<
      | {
          roles?: string[] | undefined;
          permissions?: string[] | undefined;
          sessionToken?: string | undefined;
        }
      | undefined
    >,
  ) => Promise<GetServerSidePropsResult<P>>,
  redirectTo: string = '/',
) {
  return withSSRSession(async (context, session) => {
    const user = session?.user;
    const isGlobalAdmin = (user.proxiedAs?.roles ?? user.roles)?.includes(
      'administrator',
    );

    if (!isGlobalAdmin) {
      return {
        redirect: {
          destination: redirectTo,
          permanent: false,
        },
      };
    }

    if (getServerSidePropsFunc) {
      return getServerSidePropsFunc(context, session);
    }

    return {
      props: {} as P,
    };
  });
}

export const withAdminProtection = () => withAdminRoute();
