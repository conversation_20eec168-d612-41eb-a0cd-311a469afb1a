import { useSession } from 'next-auth/react';
import { useQuery, UseQueryResult } from '@tanstack/react-query';

import proposalService from '~/shared/services/proposal';

import { Proposal } from '../types/Proposal';

export const useGetProposal = (
  uid: string | string[] | null | undefined,
): UseQueryResult<Proposal> => {
  const { data: session } = useSession();

  if (!session?.user.sessionToken || !session?.user.token) {
    throw new Error('User is not authenticated 2');
  }

  return useQuery<Proposal>({
    queryKey: [
      'getProposal',
      uid,
      session.user.token,
      session.user.sessionToken,
    ],
    queryFn: () => {
      if (!uid) {
        throw new Error('UID is required');
      }
      return proposalService.getProposalByUid(
        uid as string,
        session.user.token,
        session.user.sessionToken,
      );
    },
    enabled: !!uid,
    retry: (failureCount, error: any) => {
      if (error?.response?.status === 404 || error?.response?.status === 403) {
        return false;
      }
      return failureCount < 3;
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};
