import { useSession } from 'next-auth/react';

import {
  checkIfUserCanEdit,
  checkIfUserHasAdminPermission,
} from '../utils/checkUserPermissions';

export function useUser() {
  const { data: session } = useSession();

  const user = session?.user;

  return {
    name: user?.proxiedAs?.name ?? user?.name,
    zone: user?.proxiedAs?.zone ?? user?.zone,
    globalId: user?.proxiedAs?.globalId ?? user?.globalId,
    employeeId: user?.proxiedAs?.employeeId ?? user?.employeeId,
    managedZones: user?.proxiedAs?.managedZones ?? user?.managedZones,
    roles: user?.proxiedAs?.roles ?? user?.roles,
    language: user?.proxiedAs?.language ?? user?.language,
    persona: user?.proxiedAs?.persona ?? user?.persona,
    isProxying: !!user?.proxiedAs,
    band: user?.proxiedAs?.band ?? user?.band,
    token: user?.proxiedAs?.token ?? user?.token,
    refreshToken: user?.proxiedAs?.refreshToken ?? user?.refreshToken,
    permissions: user?.proxiedAs?.permissions ?? user?.permissions,
    sessionToken: user?.sessionToken,
    isAdmin: checkIfUserHasAdminPermission(user),
    canEdit: checkIfUserCanEdit(user),
  };
}

export type User = ReturnType<typeof useUser>;
