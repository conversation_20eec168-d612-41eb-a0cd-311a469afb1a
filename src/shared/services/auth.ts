import { api } from '~/shared/services/api';

export const getNorthstarSession = (
  token: string,
  sessionToken: string,
): Promise<any> =>
  api().get('/session', {
    headers: {
      Authorization: `Bear<PERSON> ${token}`,
      'X-Session-Token': sessionToken,
    },
    withCredentials: true,
  });

export const destroyNorthstarSession = (
  token: string,
  sessionToken: string,
): Promise<any> =>
  api().post('/signout', {
    headers: {
      Authorization: `Bearer ${token}`,
      'X-Session-Token': sessionToken,
    },
    withCredentials: true,
  });

export const stopNorthstarProxy = (
  token: string,
  sessionToken: string,
): Promise<any> =>
  api().post('/stop-proxy', {
    headers: {
      Authorization: `Bearer ${token}`,
      'X-Session-Token': sessionToken,
    },
    withCredentials: true,
  });
