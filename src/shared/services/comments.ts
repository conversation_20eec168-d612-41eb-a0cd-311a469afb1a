import axios from 'axios';

const BASE_URL = process.env.NEXT_PUBLIC_API_URL;

export interface ProposalComments {
  id: string;
  author: {
    globalId: string;
    name: string;
  };
  message: string;
  createdAt: Date;
}

export interface CreateProposalCommentDto {
  comment: string;
}

export interface ProposalCommentsListResponse {
  data: ProposalComments[];
  pageNumber?: number;
  pageSize?: number;
  totalRecords?: number;
}

class ProposalCommentsService {
  async getComments(
    token: string,
    sessionToken: string,
    proposalId: string,
    pageNumber: number = 1,
    pageSize: number = 100,
  ): Promise<ProposalCommentsListResponse> {
    try {
      const response = await axios.get<ProposalCommentsListResponse>(
        `${BASE_URL}/proposal-comments/${proposalId}?pageNumber=${pageNumber}&pageSize=${pageSize}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'X-Session-Token': sessionToken,
          },
        },
      );
      return (
        response.data || {
          data: [],
          pageNumber: 1,
          pageSize: 100,
          totalRecords: 0,
        }
      );
    } catch (error) {
      throw error;
    }
  }

  async createComment(
    proposalId: string,
    commentData: CreateProposalCommentDto,
    token: string,
    sessionToken: string,
  ): Promise<ProposalComments> {
    try {
      const response = await axios.post<ProposalComments>(
        `${BASE_URL}/proposal-comments/${proposalId}`,
        commentData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'X-Session-Token': sessionToken,
          },
        },
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async deleteComment(
    commentId: string,
    token: string,
    sessionToken: string,
  ): Promise<void> {
    try {
      await axios.delete(`${BASE_URL}/proposal-comments/${commentId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Session-Token': sessionToken,
        },
      });
    } catch (error) {
      throw error;
    }
  }
}

const proposalCommentsService = new ProposalCommentsService();
export default proposalCommentsService;
