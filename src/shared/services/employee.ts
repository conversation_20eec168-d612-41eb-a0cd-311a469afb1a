import axios from 'axios';

const BASE_URL = process.env.NEXT_PUBLIC_API_URL;

class EmployeeService {
  async getEmployeeProposals(
    token: string,
    sessionToken: string,
  ): Promise<any> {
    const response = await axios.get(`${BASE_URL}/employee/proposals`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'X-Session-Token': sessionToken,
      },
    });
    return response.data;
  }
}

const employeeService = new EmployeeService();
export default employeeService;
