import { AxiosResponse } from 'axios';

import { MenuItem } from '~/app/types';
import { api } from '~/shared/services/api';

export const getMenuItems = (
  token: string,
  sessionToken: string,
): Promise<
  AxiosResponse<{
    type: string;
    results: Array<MenuItem>;
  }>
> =>
  api().get('/v1/config/menu-items', {
    headers: {
      Authorization: `Bearer ${token}`,
      'X-Session-Token': sessionToken,
    },
  });
