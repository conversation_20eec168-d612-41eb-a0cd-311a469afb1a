import axios from 'axios';

import {
  IProposalService,
  ProposalFilters,
  ProposalFiltersResponse,
  ProposalItemsResponse,
} from '../types/ProposalService';
import { TargetTypeEnum } from '../utils/enums/target-type';

const BASE_URL = `${process.env.NEXT_PUBLIC_API_URL}/proposals`;

const withRetry = async <T>(
  fn: () => Promise<T>,
  retries: number = 3,
  delay: number = 1000,
): Promise<T> => {
  let lastError: any;

  for (let i = 0; i <= retries; i++) {
    try {
      return await fn();
    } catch (error: any) {
      lastError = error;

      if (error?.response?.status === 404 || error?.response?.status === 403) {
        throw error;
      }

      if (i < retries) {
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
      }
    }
  }

  throw lastError;
};

export class ProposalService implements IProposalService {
  async getProposals(
    token: string,
    sessionToken: string,
    filters: ProposalFilters,
  ): Promise<ProposalItemsResponse> {
    const params = new URLSearchParams();

    params.append('pageSize', filters.pageSize.toString());
    params.append('pageNumber', filters.pageNumber.toString());

    if (filters.search) {
      params.append('employeeIdentifier', filters.search);
    }

    if (filters.businessFunctions && filters.businessFunctions.length > 0) {
      filters.businessFunctions.forEach(func => {
        params.append('businessFunctions', func);
      });
    }

    if (filters.status && filters.status.length > 0) {
      filters.status.forEach(status => {
        params.append('status', status);
      });
    }

    if (filters.zones && filters.zones.length > 0) {
      filters.zones.forEach(zone => {
        params.append('zones', zone);
      });
    }

    if (filters.sltLevel && filters.sltLevel.length > 0) {
      filters.sltLevel.forEach(level => {
        params.append('sltLevel', level);
      });
    }

    if (filters.sltName && filters.sltName.length > 0) {
      filters.sltName.forEach(name => {
        params.append('sltName', name);
      });
    }

    const response = await axios.get<ProposalItemsResponse>(
      `${BASE_URL}?${params.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Session-Token': sessionToken,
        },
      },
    );

    return response.data;
  }

  async getProposalByUid(uid: string, token: string, sessionToken: string) {
    return withRetry(async () => {
      const response = await axios.get(`${BASE_URL}/${uid}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Session-Token': sessionToken,
        },
      });
      return response.data;
    });
  }

  async createTarget(
    proposalId: string,
    data: any,
    token: string,
    sessionToken: string,
  ) {
    const response = await axios.post(
      `${BASE_URL}/${proposalId}/create/targets`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Session-Token': sessionToken,
        },
      },
    );
    return response.data;
  }

  async editTarget(
    proposalId: string,
    data: any,
    token: string,
    sessionToken: string,
  ) {
    const response = await axios.put(
      `${BASE_URL}/${proposalId}/targets`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Session-Token': sessionToken,
        },
      },
    );
    return response.data;
  }

  async changeProposalStatus(
    proposalId: string,
    status: string,
    token: string,
    sessionToken: string,
  ) {
    const response = await axios.put(
      `${BASE_URL}/${proposalId}/status?status=${status}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Session-Token': sessionToken,
        },
      },
    );
    return response.data;
  }

  async deleteTargets(
    proposalId: string,
    targets: { uid: string; targetType: TargetTypeEnum }[],
    token: string,
    sessionToken: string,
  ) {
    const response = await axios.delete(`${BASE_URL}/${proposalId}/targets`, {
      data: { targets },
      headers: {
        Authorization: `Bearer ${token}`,
        'X-Session-Token': sessionToken,
      },
    });
    return response.data;
  }

  async getFilters(
    token: string,
    sessionToken: string,
  ): Promise<ProposalFiltersResponse> {
    const response = await axios.get<ProposalFiltersResponse>(
      `${BASE_URL}/filters`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Session-Token': sessionToken,
        },
      },
    );

    return response.data;
  }

  async getProposalStatus(
    proposalId: string,
    status: string = 'COMPLETED',
    token: string,
    sessionToken: string,
  ) {
    const response = await axios.get(
      `${BASE_URL}/${proposalId}/status?status=${status}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Session-Token': sessionToken,
        },
      },
    );
    return response.data;
  }
}

const proposalService = new ProposalService();
export default proposalService;
