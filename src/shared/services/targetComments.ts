import axios from 'axios';

const BASE_URL = process.env.NEXT_PUBLIC_API_URL;

export interface TargetComments {
  id: string;
  author: {
    globalId: string;
    name: string;
  };
  message: string;
  createdAt: Date;
}

export interface CreateTargetCommentDto {
  comment: string;
}

export interface TargetCommentsListResponse {
  data: TargetComments[];
  pageNumber?: number;
  pageSize?: number;
  totalRecords?: number;
}

class TargetCommentsService {
  async getComments(
    token: string,
    sessionToken: string,
    targetId: string,
    pageNumber: number = 1,
    pageSize: number = 100,
  ): Promise<TargetCommentsListResponse> {
    try {
      const response = await axios.get<TargetCommentsListResponse>(
        `${BASE_URL}/target-comments/${targetId}?pageNumber=${pageNumber}&pageSize=${pageSize}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'X-Session-Token': sessionToken,
          },
        },
      );
      return (
        response.data || {
          data: [],
          pageNumber: 1,
          pageSize: 100,
          totalRecords: 0,
        }
      );
    } catch (error) {
      throw error;
    }
  }

  async createComment(
    token: string,
    sessionToken: string,
    targetId: string,
    commentData: CreateTargetCommentDto,
  ): Promise<TargetComments> {
    try {
      const response = await axios.post<TargetComments>(
        `${BASE_URL}/target-comments/${targetId}`,
        commentData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'X-Session-Token': sessionToken,
          },
        },
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async deleteComment(
    commentId: string,
    token: string,
    sessionToken: string,
  ): Promise<void> {
    try {
      await axios.delete(`${BASE_URL}/target-comments/${commentId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Session-Token': sessionToken,
        },
      });
    } catch (error) {
      throw error;
    }
  }
}

const targetCommentsService = new TargetCommentsService();
export default targetCommentsService;
