import axios from 'axios';

const BASE_URL = `${process.env.NEXT_PUBLIC_API_URL}/target-type`;

type TargetTypeIdentifier = {
  uidTarget: string;
  uid: string;
};

export interface ReorderTargetsPayload {
  targetOrders: Array<{
    uid: string;
    order: number;
  }>;
}

export interface ReorderTargetsResponse {
  success: boolean;
  message?: string;
}

class TargetTypesService {
  async addFeedbackToTargets(
    targetUids: string[],
    token: string,
    sessionToken: string,
    agree?: boolean,
  ) {
    const payload = {
      targetUids,
      ...(typeof agree === 'boolean' ? { agree } : {}),
    };
    const response = await axios.post(`${BASE_URL}/feedback`, payload, {
      headers: {
        Authorization: `Bearer ${token}`,
        'X-Session-Token': sessionToken,
      },
    });
    return response.data;
  }

  async addFinalToTargets(
    targetUids: string[],
    token: string,
    sessionToken: string,
    agree?: boolean,
  ) {
    const payload = {
      targetUids,
      ...(typeof agree === 'boolean' ? { agree } : {}),
    };
    await axios.post(`${BASE_URL}/final`, payload, {
      headers: {
        Authorization: `Bearer ${token}`,
        'X-Session-Token': sessionToken,
      },
    });
  }

  async getAgreeStatuses(
    targetTypeIdentifiers: TargetTypeIdentifier[],
    token: string,
    sessionToken: string,
  ) {
    const payload = { targetTypeIdentifiers };
    const response = await axios.post(`${BASE_URL}/agree-status`, payload, {
      headers: {
        'Content-Type': 'application/json',
        accept: '*/*',
        Authorization: `Bearer ${token}`,
        'X-Session-Token': sessionToken,
      },
    });
    return response.data as Array<{
      uidTarget: string;
      uid: string;
      agree: boolean | null;
    }>;
  }

  async reorderTargets(
    payload: ReorderTargetsPayload,
    token: string,
    sessionToken: string,
  ): Promise<ReorderTargetsResponse> {
    try {
      const response = await axios.patch<ReorderTargetsResponse>(
        `${process.env.NEXT_PUBLIC_API_URL}/targets/order-list`,
        payload,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
            'X-Session-Token': sessionToken,
          },
        },
      );
      return response.data;
    } catch (error) {
      console.error('Error reordering targets:', error);
      throw error;
    }
  }
}

const targetTypesService = new TargetTypesService();

export default targetTypesService;
