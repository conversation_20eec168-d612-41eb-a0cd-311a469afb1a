interface Profile {
  roleId: string;
  hierarchy?: string;
  supervisory?: string;
  businessFunction?: string;
}

interface User {
  roles: string[];
  profile: Profile[];
  proxiedAs?: {
    roles: string[];
    profile: Profile[];
  };
}

const checkIfUserHasAdminPermission = (user: User) => {
  return (user?.proxiedAs?.roles ?? user?.roles ?? []).includes(
    'ADMINISTRATOR',
  );
};

const checkIfUserCanEdit = (user: User) => {
  return (user?.proxiedAs?.profile ?? user?.profile ?? []).some(
    (profile: Profile) => {
      const isAdmin = profile.roleId === 'ADMINISTRATOR';
      const canEdit =
        (profile.roleId === 'TSC' && profile.hierarchy?.includes('90000000')) ||
        (profile.roleId === 'TSCSPOCPPM' &&
          profile.hierarchy?.includes('90000000')) ||
        (profile.roleId === 'TSCSPOCPPM' &&
          profile.hierarchy?.includes('90000176'));

      return isAdmin || canEdit;
    },
  );
};

export { checkIfUserCanEdit, checkIfUserHasAdminPermission };
