import { DeliverableItem } from '~/shared/types/Deliverable';
import { Target } from '~/shared/types/Target';

export function enrichTargetsWithDeliverables(
  targets: Target[],
  deliverablesMap: Map<string, DeliverableItem>,
): Target[] {
  const enrichTarget = (target: Target): Target => {
    let enrichedTarget = { ...target };

    if (target.deliverable?.uid) {
      const completeDeliverable = deliverablesMap.get(target.deliverable.uid);
      if (completeDeliverable) {
        const normalizedType =
          completeDeliverable.type ??
          completeDeliverable.deliverableType?.code ??
          target.deliverable.type ??
          target.deliverable.deliverableType?.code;

        enrichedTarget = {
          ...enrichedTarget,
          deliverable: {
            ...target.deliverable,
            ...completeDeliverable,
            type: normalizedType,
            deliverableType:
              completeDeliverable.deliverableType ??
              target.deliverable.deliverableType ??
              (normalizedType ? { code: normalizedType } : undefined),
          },
        };
      }
    }

    if (target.children && target.children.length > 0) {
      enrichedTarget = {
        ...enrichedTarget,
        children: target.children.map(enrichTarget),
      };
    }

    return enrichedTarget;
  };

  return targets.map(enrichTarget);
}

export function createDeliverablesMap(
  deliverables: DeliverableItem[],
): Map<string, DeliverableItem> {
  const map = new Map<string, DeliverableItem>();
  deliverables.forEach(deliverable => {
    map.set(deliverable.uid, deliverable);
  });
  return map;
}

export function hasProjectDeliverables(target: Target): boolean {
  return (
    target.deliverable?.type === 'PROJECT' &&
    Array.isArray(target.deliverable.deliverables) &&
    target.deliverable.deliverables.length > 0
  );
}

export function getProjectDeliverablesSimple(
  target: Target,
): Array<{ uid: string; name: string }> {
  if (!hasProjectDeliverables(target)) {
    return [];
  }

  const deliverables = target.deliverable?.deliverables || [];
  return deliverables.map(d => ({
    uid: d.uid,
    name: d.name || 'Unnamed Deliverable',
  }));
}
