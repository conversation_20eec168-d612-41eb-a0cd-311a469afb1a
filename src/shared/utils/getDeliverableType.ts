import { DeliverableItem } from '~/shared/types/Deliverable';
import { DeliverableTypeEnum } from '~/shared/types/DeliverableType';
import { Target } from '~/shared/types/Target';

export function getDeliverableType(
  data: Target | DeliverableItem | undefined,
  isEdit: boolean,
): DeliverableTypeEnum {
  if (!data) {
    return DeliverableTypeEnum.KPI;
  }

  if (isEdit) {
    const target = data as Target;
    return (
      target.deliverable?.type ??
      target.deliverable?.deliverableType?.code ??
      DeliverableTypeEnum.KPI
    );
  }

  const deliverable = data as DeliverableItem;
  return (
    deliverable.type ??
    deliverable.deliverableType?.code ??
    DeliverableTypeEnum.KPI
  );
}

export function isProjectDeliverableType(
  data: Target | DeliverableItem | undefined,
  isEdit: boolean,
): boolean {
  return getDeliverableType(data, isEdit) === DeliverableTypeEnum.PROJECT;
}

export function getDeliverableUsage(
  data: Target | DeliverableItem | undefined,
  isEdit: boolean,
): number {
  if (!data) {
    return 0;
  }

  if (isEdit) {
    const target = data as Target;
    return target.deliverable?.usage ?? 0;
  }

  const deliverable = data as DeliverableItem;
  return deliverable.usage ?? 0;
}
