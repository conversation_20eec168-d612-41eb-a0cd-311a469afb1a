import { DeliverableItem } from '../types/Deliverable';
import { DeliverableTypeEnum } from '../types/DeliverableType';
import { Target } from '../types/Target';
import { ProposalStatusEnum } from '../utils/enums';
import { TargetTypeEnum } from '../utils/enums/target-type';

export function getTargetCardFooterText(
  proposalStatus?: ProposalStatusEnum,
  currentTargetType?: TargetTypeEnum,
  targetData?: Target,
): string {
  if (!proposalStatus || !currentTargetType || !targetData?.targetTypes) {
    return '';
  }

  const hasProposal = targetData.targetTypes.some(
    t => t.type === TargetTypeEnum.PROPOSAL,
  );
  const hasFeedback = targetData.targetTypes.some(
    t => t.type === TargetTypeEnum.FEEDBACK,
  );
  const hasFinal = targetData.targetTypes.some(
    t => t.type === TargetTypeEnum.FINAL,
  );

  const isOnlyProposal = hasProposal && !hasFeedback && !hasFinal;
  const isProposalFeedback = hasProposal && hasFeedback && !hasFinal;
  const isAllThreeTypes = hasProposal && hasFeedback && hasFinal;

  switch (proposalStatus) {
    case ProposalStatusEnum.NOT_STARTED:
      return '';

    case ProposalStatusEnum.IN_PROGRESS_PROPOSAL:
      // No badges shown during proposal phase
      return '';

    case ProposalStatusEnum.IN_PROGRESS_FEEDBACK:
      if (currentTargetType === TargetTypeEnum.PROPOSAL) {
        return '';
      }
      return '';

    case ProposalStatusEnum.IN_PROGRESS_FINAL:
      if (currentTargetType === TargetTypeEnum.PROPOSAL) {
        if (isProposalFeedback || isAllThreeTypes) {
          return 'I Agree';
        }
        if (isOnlyProposal) {
          return "Don't agree";
        }
      }
      if (currentTargetType === TargetTypeEnum.FEEDBACK) {
        if (isAllThreeTypes || isProposalFeedback) {
          return 'I Agree';
        }
      }
      return '';

    case ProposalStatusEnum.COMPLETED:
      if (currentTargetType === TargetTypeEnum.PROPOSAL) {
        if (isProposalFeedback || isAllThreeTypes) {
          return 'I Agree';
        }
        if (isOnlyProposal) {
          return "Don't agree";
        }
      }
      if (currentTargetType === TargetTypeEnum.FEEDBACK) {
        if (isAllThreeTypes) {
          return 'I Agree';
        }
        if (isProposalFeedback) {
          return "Don't agree";
        }
      }
      if (currentTargetType === TargetTypeEnum.FINAL) {
        if (isAllThreeTypes) {
          return 'I Agree';
        }
      }
      return '';

    default:
      return '';
  }
}

export function isProjectTypeTarget(target: Target): boolean {
  if (!target || !target.deliverable) {
    return false;
  }

  try {
    const deliverableTypeCode = target.deliverable.deliverableType?.code;
    const deliverableType = target.deliverable.type;

    return (
      deliverableTypeCode === DeliverableTypeEnum.PROJECT ||
      deliverableType === DeliverableTypeEnum.PROJECT
    );
  } catch (error) {
    return false;
  }
}

export function getProjectDeliverables(target: Target): DeliverableItem[] {
  if (!isProjectTypeTarget(target)) {
    return [];
  }

  try {
    const deliverables = target.deliverable?.deliverables;

    if (!deliverables || !Array.isArray(deliverables)) {
      return [];
    }

    return deliverables.filter(
      (deliverable): deliverable is DeliverableItem =>
        deliverable &&
        typeof deliverable === 'object' &&
        typeof deliverable.uid === 'string' &&
        deliverable.uid.length > 0,
    );
  } catch (error) {
    return [];
  }
}

export function shouldDisplayDeliverables(target: Target): boolean {
  if (!target) {
    return false;
  }

  try {
    return (
      isProjectTypeTarget(target) && getProjectDeliverables(target).length > 0
    );
  } catch (error) {
    return false;
  }
}
